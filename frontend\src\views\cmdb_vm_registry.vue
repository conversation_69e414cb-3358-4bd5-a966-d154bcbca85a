<style lang="scss" scoped>
.user-manage {
  .search-card {
    margin-bottom: 10px;
    height: auto;

    // label-width:130px;
  }

  :deep(.el-table) {

    // 滚动条加粗
    .el-scrollbar__bar.is-horizontal {
      height: 10px;
      left: 2px;
    }

    .el-scrollbar__bar.is-vertical {
      top: 2px;
      width: 10px;
    }

    .cell {
      display: inline-block; // 确保 max-width 生效
      white-space: nowrap;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .cell:hover {
      white-space: pre-wrap;
      /* 使用pre-wrap来允许换行但保留空白字符 */
      overflow: visible;
      text-overflow: clip;
      word-break: keep-all;
      /* 尽量保持单词完整，不强制断开 */
      max-width: 400px;
      /* 保持最大宽度不变 */
      width: 100%;
      /* 确保宽度一致 */
      word-wrap: break-word;
      /* 当单词超过容器宽度时允许换行 */
      display: inline-block;
      /* 确保元素可以正确处理宽度 */
    }

    // 表头样式
    th .cell {
      white-space: nowrap !important; // 强制表头内容不换行
      display: flex;
      align-items: center; // 垂直居中对齐
    }
  }

  .pagination {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
  }
}

.form-control {
  width: auto;
  /* 占满父容器宽度 */
  min-width: 190px;
}
</style>

<template>
  <div class="user-manage">
    <el-dialog v-model="dialogVisible.add" title="新增虚拟机信息" width="400" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="dialogdiv">
        <el-form :model="formData" :rules="rules" ref="addFormRef" label-position="right" status-icon>
          <!-- 管理IP -->
          <el-form-item prop="management_ip" label="管理IP:">
            <el-input v-model="formData.management_ip" style="width: 240px" clearable placeholder="请输入管理IP" />
          </el-form-item>

          <!-- 主机名 -->
          <el-form-item prop="hostname" label="主机名:">
            <el-input v-model="formData.hostname" style="width: 240px" clearable placeholder="请输入主机名" />
          </el-form-item>

          <!-- 功能用途 -->
          <el-form-item prop="function_purpose" label="功能用途:">
            <el-input v-model="formData.function_purpose" style="width: 240px" type="textarea" clearable
              placeholder="请输入功能用途" />
          </el-form-item>

          <!-- 管理员1 -->
          <el-form-item prop="admin1" label="管理员1:">
            <el-select v-model="formData.admin1" style="width: 240px" placeholder="请选择管理员1" clearable filterable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
            </el-select>
          </el-form-item>

          <!-- 管理员2 -->
          <el-form-item prop="admin2" label="管理员2:">
            <el-select v-model="formData.admin2" style="width: 240px" placeholder="请选择管理员2" clearable filterable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
            </el-select>
          </el-form-item>

          <!-- ESXi主机IP -->
          <el-form-item prop="host_ip" label="ESXi主机IP:">
            <!-- 有VMware关联时显示关联的ESXi主机IP -->
            <div v-if="vmwareAssociation.hasAssociation" style="display: flex; align-items: center; gap: 8px;">
              <el-input v-model="vmwareAssociation.esxiIp" style="width: 180px" readonly />
              <el-tag type="success" size="small">已关联</el-tag>
            </div>
            <!-- 无VMware关联时显示手动输入和查询功能 -->
            <div v-else style="display: flex; align-items: center; gap: 8px;">
              <div style="position: relative; width: 180px;">
                <el-input v-model="formData.host_ip" style="width: 100%" clearable placeholder="请输入ESXi主机IP"
                  :readonly="zabbixHostInfo.loading" />
                <div v-if="zabbixHostInfo.loading"
                  style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); color: #409eff;">
                  <el-icon class="is-loading">
                    <Loading />
                  </el-icon>
                </div>
              </div>
              <el-button size="small" type="primary" :loading="zabbixHostInfo.loading"
                :disabled="!formData.management_ip || !zabbixHostInfo.enabled" @click="queryHostIpManually"
                title="从外部接口查询ESXi主机IP">
                <el-icon>
                  <Refresh />
                </el-icon>
              </el-button>
            </div>
            <!-- 提示信息 -->
            <div v-if="vmwareAssociation.hasAssociation" style="color: #67c23a; font-size: 12px; margin-top: 4px;">
              ESXi主机IP已通过VMware虚拟化管理系统自动关联
            </div>
            <div v-else-if="zabbixHostInfo.loading && !zabbixHostInfo.error"
              style="color: #409eff; font-size: 12px; margin-top: 4px;">
              正在查询ESXi主机IP...
            </div>
            <div v-else-if="zabbixHostInfo.error" style="color: #f56c6c; font-size: 12px; margin-top: 4px;">
              {{ zabbixHostInfo.error }}
            </div>
            <div v-else-if="zabbixHostInfo.lastUpdated && !zabbixHostInfo.loading"
              style="color: #909399; font-size: 12px; margin-top: 4px;">
              最后更新: {{ zabbixHostInfo.lastUpdated }}
            </div>
            <div v-else-if="!zabbixHostInfo.enabled" style="color: #e6a23c; font-size: 12px; margin-top: 4px;">
              外部接口服务未启用，请手动输入ESXi主机IP
            </div>
          </el-form-item>
          <!-- 操作系统 -->
          <el-form-item prop="operating_system" label="操作系统:">
            <el-select v-model="formData.operating_system" style="width: 240px" clearable filterable
              placeholder="请选择操作系统">
              <el-option v-for="item in operatingsystems" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>
          <!-- 所属机房1 -->
          <el-form-item prop="data_center1" label="所属机房:">
            <el-select v-model="formData.data_center1" style="width: 240px" clearable filterable placeholder="请选择所属机房">
              <el-option v-for="item in datacenter1s" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 生命周期 -->
          <el-form-item prop="operation_status" label="生命周期:">
            <el-select v-model="formData.operation_status" style="width: 240px" clearable filterable
              placeholder="请选择生命周期">
              <el-option v-for="item in operationstatus" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 需要监控 -->
          <el-form-item prop="monitoring_requirement" label="需要监控:">
            <el-select v-model="formData.monitoring_requirement" style="width: 240px" clearable filterable
              placeholder="请选择需要监控" @change="handleMonitoringRequirementChange">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>

          <!-- 不监控原因 -->
          <el-form-item prop="monitoring_requirement_description" label="不监控原因:"
            :required="formData.monitoring_requirement === '否'">
            <el-input v-model="formData.monitoring_requirement_description" style="width: 240px" type="textarea"
              :rows="3" clearable placeholder="当需要监控为否时必填" :disabled="formData.monitoring_requirement === '是'" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.add = false">返回</el-button>
          <el-button type="primary" @click="validateAndSubmitAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible.edit" title="编辑虚拟机信息" width="400" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="dialogdiv">
        <el-form :model="formData" :rules="rules" ref="editFormRef" label-position="right" status-icon>
          <!-- 管理IP -->
          <el-form-item prop="management_ip" label="管理IP:">
            <el-input v-model="formData.management_ip" style="width: 240px" clearable placeholder="请输入管理IP" />
          </el-form-item>

          <!-- 主机名 -->
          <el-form-item prop="hostname" label="主机名:">
            <el-input v-model="formData.hostname" style="width: 240px" clearable placeholder="请输入主机名" />
          </el-form-item>

          <!-- 功能用途 -->
          <el-form-item prop="function_purpose" label="功能用途:">
            <el-input v-model="formData.function_purpose" style="width: 240px" type="textarea" clearable
              placeholder="请输入功能用途" />
          </el-form-item>

          <!-- 管理员1 -->
          <el-form-item prop="admin1" label="管理员1:">
            <el-select v-model="formData.admin1" style="width: 240px" placeholder="请选择管理员1" clearable filterable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
            </el-select>
          </el-form-item>

          <!-- 管理员2 -->
          <el-form-item prop="admin2" label="管理员2:">
            <el-select v-model="formData.admin2" style="width: 240px" placeholder="请选择管理员2" clearable filterable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
            </el-select>
          </el-form-item>

          <!-- ESXi主机IP -->
          <el-form-item prop="host_ip" label="ESXi主机IP:">
            <!-- 有VMware关联时显示关联的ESXi主机IP -->
            <div v-if="vmwareAssociation.hasAssociation" style="display: flex; align-items: center; gap: 8px;">
              <el-input v-model="vmwareAssociation.esxiIp" style="width: 180px" readonly />
              <el-tag type="success" size="small">已关联</el-tag>
            </div>
            <!-- 无VMware关联时显示手动输入和查询功能 -->
            <div v-else style="display: flex; align-items: center; gap: 8px;">
              <div style="position: relative; width: 180px;">
                <el-input v-model="formData.host_ip" style="width: 100%" clearable placeholder="请输入ESXi主机IP"
                  :readonly="zabbixHostInfo.loading" />
                <div v-if="zabbixHostInfo.loading"
                  style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); color: #409eff;">
                  <el-icon class="is-loading">
                    <Loading />
                  </el-icon>
                </div>
              </div>
              <el-button size="small" type="primary" :loading="zabbixHostInfo.loading"
                :disabled="!formData.management_ip || !zabbixHostInfo.enabled" @click="queryHostIpManually"
                title="从外部接口查询ESXi主机IP">
                <el-icon>
                  <Refresh />
                </el-icon>
              </el-button>
            </div>
            <!-- 提示信息 -->
            <div v-if="vmwareAssociation.hasAssociation" style="color: #67c23a; font-size: 12px; margin-top: 4px;">
              ESXi主机IP已通过VMware虚拟化管理系统自动关联
            </div>
            <div v-else-if="zabbixHostInfo.loading && !zabbixHostInfo.error"
              style="color: #409eff; font-size: 12px; margin-top: 4px;">
              正在查询ESXi主机IP...
            </div>
            <div v-else-if="zabbixHostInfo.error" style="color: #f56c6c; font-size: 12px; margin-top: 4px;">
              {{ zabbixHostInfo.error }}
            </div>
            <div v-else-if="zabbixHostInfo.lastUpdated && !zabbixHostInfo.loading"
              style="color: #909399; font-size: 12px; margin-top: 4px;">
              最后更新: {{ zabbixHostInfo.lastUpdated }}
            </div>
            <div v-else-if="!zabbixHostInfo.enabled" style="color: #e6a23c; font-size: 12px; margin-top: 4px;">
              外部接口服务未启用，请手动输入ESXi主机IP
            </div>
          </el-form-item>

          <!-- 操作系统 -->
          <el-form-item prop="operating_system" label="操作系统:">
            <el-select v-model="formData.operating_system" style="width: 240px" clearable filterable
              placeholder="请选择操作系统">
              <el-option v-for="item in operatingsystems" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 所属机房1 -->
          <el-form-item prop="data_center1" label="所属机房:">
            <el-select v-model="formData.data_center1" style="width: 240px" clearable filterable placeholder="请选择所属机房">
              <el-option v-for="item in datacenter1s" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 生命周期 -->
          <el-form-item prop="operation_status" label="生命周期:">
            <el-select v-model="formData.operation_status" style="width: 240px" clearable filterable
              placeholder="请选择生命周期">
              <el-option v-for="item in operationstatus" :key="item.dict_code" :label="item.dict_name"
                :value="item.dict_code" />
            </el-select>
          </el-form-item>

          <!-- 需要监控 -->
          <el-form-item prop="monitoring_requirement" label="需要监控:">
            <el-select v-model="formData.monitoring_requirement" style="width: 240px" clearable filterable
              placeholder="请选择需要监控" @change="handleMonitoringRequirementChange">
              <el-option label="是" value="是" />
              <el-option label="否" value="否" />
            </el-select>
          </el-form-item>

          <!-- 不监控原因 -->
          <el-form-item prop="monitoring_requirement_description" label="不监控原因:"
            :required="formData.monitoring_requirement === '否'">
            <el-input v-model="formData.monitoring_requirement_description" style="width: 240px" type="textarea"
              :rows="3" clearable placeholder="当需要监控为否时必填" :disabled="formData.monitoring_requirement === '是'" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.edit = false">取消</el-button>
          <el-button type="primary" @click="validateAndSubmitEdit">更新</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible.delete" title="删除管理IP" width="500" align-center :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-alert type="warning" :title="`确定要删除 IP 为 ${formData.management_ip} 的记录吗？`" :closable="false" />
      <template #footer>
        <div>
          <el-button @click="dialogVisible.delete = false">取消</el-button>
          <el-button type="danger" @click="submitDelete">确认删除</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true">
        <el-row :gutter="10">
          <!-- 第一行 -->
          <el-col :span="6">
            <el-form-item label="管理IP">
              <el-input v-model="search.management_ip" placeholder="请输入管理IP" clearable class="form-control" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="ESXi主机IP">
              <el-input v-model="search.host_ip" placeholder="请输入ESXi主机IP" clearable class="form-control" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主机名">
              <el-input v-model="search.hostname" placeholder="请输入主机名" clearable class="form-control" />
            </el-form-item>
          </el-col>


          <!-- 第二行 -->
          <el-col :span="6">
            <el-form-item label="管理员1">
              <el-select v-model="search.admin1" placeholder="请选择管理员1" clearable filterable class="form-control">
                <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="管理员2">
              <el-select v-model="search.admin2" placeholder="请选择管理员2" clearable filterable class="form-control">
                <el-option v-for="item in usersList" :key="item.id" :label="item.real_name" :value="item.real_name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属机房">
              <el-select v-model="search.data_center1" placeholder="请选择所属机房" clearable filterable class="form-control">
                <el-option v-for="item in datacenter1s" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="操作系统">
              <el-select v-model="search.operating_system" placeholder="请选择操作系统" clearable filterable class="form-control">
                <el-option v-for="item in operatingsystems" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 第三行 -->
          <el-col :span="6">
            <el-form-item label="需要监控">
              <el-select v-model="search.monitoring_requirement" placeholder="请选择需要监控" clearable filterable
                class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="监控状态">
              <el-select v-model="search.is_monitored" placeholder="请选择监控状态" clearable filterable class="form-control">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="生命周期">
              <el-select v-model="search.operation_status" placeholder="请选择生命周期" clearable filterable
                class="form-control">
                <el-option v-for="item in operationstatus" :key="item.dict_code" :label="item.dict_name"
                  :value="item.dict_name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="PING状态">
              <el-select v-model="search.online_status" placeholder="请选择PING状态" clearable filterable
                class="form-control">
                <el-option label="在线" value="在线" />
                <el-option label="离线" value="离线" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="search-buttons-col">
            <el-form-item label=" " class="form-item-with-label search-buttons">
              <div class="button-container">
                <el-button type="primary" @click="loadData">
                  <el-icon>
                    <Search />
                  </el-icon>查询
                </el-button>
                <el-button @click="resetSearch">重置</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区 -->
    <div class="action-bar unified-action-bar">
      <div class="action-bar-left">
        <el-button type="success" :disabled="!hasInsertPermission" @click="handleAdd">
          <el-icon>
            <Plus />
          </el-icon>新增资产
        </el-button>
      </div>
      <div class="action-bar-right">
        <el-button type="info" @click="exportData">
          <el-icon>
            <Download />
          </el-icon> 导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="userArr" ref="table" border stripe v-loading="loading" table-layout="auto"
        @sort-change="handleSortChange">
        <!-- 主键 -->
        <el-table-column prop="id" label="序号" v-if="false"></el-table-column>

        <!-- 管理IP -->
        <el-table-column prop="management_ip" label="管理IP" sortable></el-table-column>

        <!-- 主机名 -->
        <el-table-column prop="hostname" label="主机名" sortable></el-table-column>

        <!-- 功能用途 -->
        <el-table-column prop="function_purpose" label="功能用途" sortable></el-table-column>

        <!-- 管理员1 -->
        <el-table-column prop="admin1" label="管理员1" sortable></el-table-column>

        <!-- 管理员2 -->
        <el-table-column prop="admin2" label="管理员2" sortable></el-table-column>

        <!-- ESXi主机IP -->
        <el-table-column prop="host_ip_display" label="ESXi主机IP" sortable>
          <template #default="scope">
            <span v-if="scope.row.host_ip_display">{{ scope.row.host_ip_display }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>

        <!-- 操作系统 -->
        <el-table-column prop="operating_system" label="操作系统" sortable></el-table-column>

        <!-- 所属机房1 -->
        <el-table-column prop="data_center1" label="所属机房" sortable></el-table-column>

        <!-- 生命周期 -->
        <el-table-column prop="operation_status" label="生命周期" sortable>
          <template #default="scope">
            <el-tag :type="getLifecycleTagType(scope.row.operation_status)">
              {{ scope.row.operation_status }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 需要监控 -->
        <el-table-column prop="monitoring_requirement" label="需要监控" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.monitoring_requirement === '是' ? 'success' : 'danger'">
              {{ scope.row.monitoring_requirement }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 不监控原因 -->
        <el-table-column prop="monitoring_requirement_description" label="不监控原因" sortable></el-table-column>

        <!-- 监控状态 -->
        <el-table-column prop="is_monitored" label="监控状态" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.is_monitored === '是' ? 'success' : 'danger'">
              {{ scope.row.is_monitored }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- PING状态 -->
        <el-table-column prop="online_status" label="PING状态" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.online_status === '在线' ? 'success' : 'danger'">
              {{ scope.row.online_status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" sortable></el-table-column>
        <el-table-column prop="created_by" label="创建人" sortable></el-table-column>
        <el-table-column prop="updated_at" label="更新时间" sortable></el-table-column>
        <el-table-column prop="updated_by" label="更新人" sortable></el-table-column>
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <div style="display: flex; white-space: nowrap">
              <el-button size="small" type="warning" :disabled="!hasUpdatePermission"
                @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button size="small" type="danger" :disabled="!hasDeletePermission"
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination background :current-page="search.currentPage" :page-size="search.pageSize" :total="search.total"
          :page-sizes="[10, 20, 50, 100, 1000, 10000]" :pager-count="5" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange" @current-change="handlePageChange" />
      </div>
    </el-card>
  </div>
</template>

<script>
import { Plus, Search, Download, Refresh, Loading } from "@element-plus/icons-vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import {
  getZabbixHostInfoWithCache,
  clearZabbixHostInfoCache,
  createDebouncedHostInfoQuery,
  getZabbixConfigStatus
} from "@/api/zabbix-host-info";

export default {
  components: {
    Plus,
    Search,
    Download,
    Refresh,
    Loading,
  },

  data() {
    return {
      userArr: [], // 监控IP列表
      loading: false, // 加载状态
      operatingsystems: [], // 数据字典值
      datacenter1s: [], //数据字典值
      operationstatus: [], // 数据字典值 - 生命周期状态
      usersList: [], // 用户列表
      hasDeletePermission: localStorage.getItem("role_code")?.includes("D"), // 是否有删除权限
      hasUpdatePermission: localStorage.getItem("role_code")?.includes("U"), // 是否有删除权限
      hasInsertPermission: localStorage.getItem("role_code")?.includes("I"), // 是否有删除权限

      // 对话框状态
      dialogVisible: {
        add: false,
        edit: false,
        delete: false,
      },
      // 查询数据
      search: {
        management_ip: "",
        hostname: "",
        admin1: "",
        admin2: "",
        host_ip: "",
        data_center1: "",
        operating_system: "", // 新增：操作系统查询条件
        monitoring_requirement: "", // 新增：需要监控搜索字段
        is_monitored: "",
        operation_status: "", // 生命周期搜索字段
        online_status: "", // PING状态

        total: 0, // 总记录数
        pageSize: 10, // 每页显示条目数
        currentPage: 1, // 当前页码
        sortProp: "updated_at", // 排序字段
        sortOrder: "desc", // 排序顺序
      },

      // 表单数据
      formData: {
        id: null,
        management_ip: "",
        hostname: "",
        function_purpose: "",
        admin1: "",
        admin2: "",
        host_ip: "",
        host_ip_display: "", // 新增：用于显示关联的ESXi主机IP
        operating_system: "",
        data_center1: "", // 公式自动获取，禁用输入
        operation_status: "D00035", // 生命周期状态，默认值
        //admin: "", // 公式自动获取，禁用输入
        app_system_id: "", // 公式自动获取，禁用输入
        virtual_host_ip: "", // 公式自动获取，禁用输入
        data_center2: "", // 公式自动获取，禁用输入
        monitoring_requirement: "是", // 新增：需要监控，默认值为"是"
        monitoring_requirement_description: "", // 新增：不监控原因
        is_monitored: "是", // 默认值
      },

      // 关联状态
      vmwareAssociation: {
        hasAssociation: false, // 是否有VMware关联
        esxiIp: "", // 关联的ESXi主机IP
      },
      // 表单验证规则
      rules: {
        management_ip: [
          { required: true, message: '请输入管理IP', trigger: 'blur' },
          { pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/, message: '请输入正确的IP地址格式', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }

              // 使用箭头函数保持this上下文
              this.checkVmManagementIpDuplicate(value, this.formData.id || null)
                .then(result => {
                  if (result.exists) {
                    callback(new Error(result.msg));
                  } else {
                    callback();
                  }
                })
                .catch(error => {
                  console.error('检查虚拟机管理IP失败:', error);
                  callback(new Error('检查虚拟机管理IP失败，请稍后重试'));
                });
            },
            trigger: 'blur'
          }
        ],
        hostname: [
          { required: true, message: '请输入主机名', trigger: 'blur' },
          { min: 2, max: 50, message: '主机名长度应在2-50个字符之间', trigger: 'blur' }
        ],
        function_purpose: [
          { required: true, message: '请输入功能用途', trigger: 'blur' }
        ],
        admin1: [
          { required: true, message: '请选择管理员1', trigger: 'change' }
        ],
        operating_system: [
          { required: true, message: '请选择操作系统', trigger: 'change' }
        ],
        data_center1: [
          { required: true, message: '请选择所属机房', trigger: 'change' }
        ],
        operation_status: [
          { required: true, message: '请选择生命周期', trigger: 'change' }
        ],
        monitoring_requirement: [
          { required: true, message: '请选择需要监控', trigger: 'change' }
        ],
        monitoring_requirement_description: [
          { validator: this.validateMonitoringDescription, trigger: 'blur' }
        ]
      },

      // ZabbixESXi主机IP查询相关状态
      zabbixHostInfo: {
        loading: false, // 查询加载状态
        enabled: false, // Zabbix服务是否启用
        lastUpdated: null, // 最后更新时间
        error: null // 错误信息
      },
    };
  },

  watch: {
    // 监听管理IP变化，自动查询ESXi主机IP和检查VMware关联
    'formData.management_ip': {
      handler(newVal, oldVal) {
        // 清除之前的错误信息
        this.zabbixHostInfo.error = null;

        // 如果管理IP被清空，也清空ESXi主机IP和VMware关联
        if (!newVal) {
          this.formData.host_ip = '';
          this.zabbixHostInfo.lastUpdated = null;
          this.vmwareAssociation.hasAssociation = false;
          this.vmwareAssociation.esxiIp = "";
          return;
        }

        // 验证IP格式
        const ipPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
        if (!ipPattern.test(newVal)) {
          return; // 不是有效IP格式，不触发查询
        }

        // 只有在值真正改变且不为空时才触发检查
        if (newVal !== oldVal) {
          // 首先检查VMware关联
          this.checkVmwareAssociation(newVal);

          // 如果启用了Zabbix查询，也进行自动查询
          if (this.zabbixHostInfo.enabled) {
            this.autoQueryHostIp(newVal);
          }
        }
      },
      immediate: false
    }
  },

  mounted() {
    // 检查URL参数中是否有search_ip
    if (this.$route.query.search_ip) {
      this.search.management_ip = this.$route.query.search_ip;
    }

    this.loadData();
    this.getDatadict("K", "operatingsystems");
    this.getDatadict("A", "datacenter1s");
    this.getDatadict("D", "operationstatus"); // 加载生命周期状态数据字典
    this.loadUsersList(); // 加载用户列表

    // 检查Zabbix配置状态
    this.checkZabbixConfigStatus();

    // 创建防抖的ESXi主机IP查询函数
    this.debouncedHostInfoQuery = createDebouncedHostInfoQuery(1500);

    // 检查是否来自发现结果页面
    if (this.$route.query.from_discovery === 'true') {
      this.$nextTick(() => {
        this.handleAddFromDiscovery();
      });
    }
  },
  methods: {
    // 页码选择
    handlePageChange(newPage) {
      this.search.currentPage = newPage;
      this.loadData();
    },

    // 每页显示条目数变化
    handlePageSizeChange(newSize) {
      this.search.pageSize = parseInt(newSize);
      this.search.currentPage = 1; // 重置当前页码为第一页
      this.loadData();
    },

    //增加排序
    handleSortChange({ prop, order }) {
      this.search.sortProp = prop;
      this.search.sortOrder = order === "ascending" ? "asc" : "desc";
      this.loadData();
    },

    // 加载数据
    async loadData() {
      try {
        this.loading = true;
        const response = await this.$axios.post(
          `/api/get_cmdb_vm_registry`,
          this.search
        );
        this.userArr = response.data.msg;
        this.search.total = response.data.total;
      } catch (error) {
        console.error("数据加载失败:", error);
        this.$message.error("数据加载失败");
      } finally {
        this.loading = false;
      }
    },

    // 验证并添加虚拟机信息
    async validateAndSubmitAdd() {
      try {
        // 表单验证
        await this.$refs.addFormRef.validate();

        // 验证通过，提交表单
        await this.submitAdd();
      } catch (error) {
        // 验证失败，显示错误信息
        this.$message.error("请完善必填项后再提交");
        // 在开发环境中保留详细的错误信息，生产环境可以移除
        if (process.env.NODE_ENV === 'development') {
          console.error("表单验证失败:", error);
        }
      }
    },

    // 添加虚拟机信息
    async submitAdd() {
      try {
        // 添加username参数，用于记录创建者
        // 确保提交所有后端期望的字段，即使是空值
        const requestData = {
          management_ip: this.formData.management_ip,
          hostname: this.formData.hostname,
          function_purpose: this.formData.function_purpose,
          admin1: this.formData.admin1,
          admin2: this.formData.admin2 || "",
          // 如果有VMware关联，使用关联的ESXi主机IP，否则使用手动输入的值
          host_ip: this.vmwareAssociation.hasAssociation ? this.vmwareAssociation.esxiIp : (this.formData.host_ip || ""),
          operating_system: this.formData.operating_system,
          data_center1: this.formData.data_center1 || "",
          operation_status: this.formData.operation_status, // 生命周期状态 - 修复：添加缺失的字段
          // 以下字段在后端期望接收，但在前端不需要用户输入
          admin: "", // 由后端自动处理
          app_system_id: "", // 由后端自动处理
          virtual_host_ip: "", // 由后端自动处理
          data_center2: "", // 由后端自动处理
          monitoring_requirement: this.formData.monitoring_requirement, // 新增：需要监控
          monitoring_requirement_description: this.formData.monitoring_requirement_description || "", // 新增：不监控原因
          is_monitored: "是", // 默认值
          username: localStorage.getItem("loginUsername") || "unknown"
        };



        await this.$axios.post(`/api/add_cmdb_vm_registry`, requestData);
        this.$message.success("添加成功");
        this.dialogVisible.add = false;
        this.loadData();
      } catch (error) {
        console.error("添加失败:", error);
        this.$message.error(error.response?.data?.msg || "添加失败");
      }
    },

    // 验证并编辑虚拟机信息
    async validateAndSubmitEdit() {
      try {
        // 表单验证
        await this.$refs.editFormRef.validate();

        // 验证通过，提交表单
        await this.submitEdit();
      } catch (error) {
        // 验证失败，显示错误信息
        this.$message.error("请完善必填项后再提交");
        // 在开发环境中保留详细的错误信息，生产环境可以移除
        if (process.env.NODE_ENV === 'development') {
          console.error("表单验证失败:", error);
        }
      }
    },

    // 编辑虚拟机信息
    async submitEdit() {
      try {
        // 添加username参数，用于记录更新者
        // 确保提交所有后端期望的字段，即使是空值
        const requestData = {
          id: this.formData.id,
          management_ip: this.formData.management_ip,
          hostname: this.formData.hostname,
          function_purpose: this.formData.function_purpose,
          admin1: this.formData.admin1,
          admin2: this.formData.admin2 || "",
          // 如果有VMware关联，使用关联的ESXi主机IP，否则使用手动输入的值
          host_ip: this.vmwareAssociation.hasAssociation ? this.vmwareAssociation.esxiIp : (this.formData.host_ip || ""),
          operating_system: this.formData.operating_system,
          data_center1: this.formData.data_center1 || "",
          operation_status: this.formData.operation_status, // 生命周期状态
          // 以下字段在后端期望接收，但在前端不需要用户输入
          admin: "", // 由后端自动处理
          app_system_id: "", // 由后端自动处理
          virtual_host_ip: "", // 由后端自动处理
          data_center2: "", // 由后端自动处理
          monitoring_requirement: this.formData.monitoring_requirement, // 新增：需要监控
          monitoring_requirement_description: this.formData.monitoring_requirement_description || "", // 新增：不监控原因
          is_monitored: "是", // 默认值
          username: localStorage.getItem("loginUsername") || "unknown"
        };



        await this.$axios.post(`/api/update_cmdb_vm_registry`, requestData);
        this.$message.success("更新成功");
        this.dialogVisible.edit = false;
        this.loadData();
      } catch (error) {
        console.error("更新失败:", error);
        this.$message.error(error.response?.data?.msg || "更新失败");
      }
    },

    // 删除监控IP
    async submitDelete() {
      try {
        // 添加username参数，用于记录删除者
        const requestData = {
          ...this.formData,
          username: localStorage.getItem("loginUsername") || "unknown"
        };
        await this.$axios.post(`/api/del_cmdb_vm_registry`, requestData);
        this.$message.success("删除成功");
        this.loadData();
        this.dialogVisible.delete = false;
      } catch (error) {
        console.error("删除失败:", error);
        this.$message.error(error.response?.data?.msg || "删除失败");
      }
    },

    // 重置搜索条件
    resetSearch() {
      this.search = {
        management_ip: "",
        hostname: "",
        admin1: "",
        admin2: "",
        host_ip: "",
        operating_system: "",
        data_center1: "",
        monitoring_requirement: "", // 新增：需要监控搜索字段
        is_monitored: "",
        operation_status: "", // 生命周期搜索字段
        online_status: "", // PING状态
        total: 0,
        pageSize: 10,
        currentPage: 1,
        sortProp: "updated_at",
        sortOrder: "desc",
      };
      this.loadData();
    },

    // 得到数据字典
    async getDatadict(dictCode, targetArray) {
      try {
        const response = await this.$axios.post(
          `/api/get_cmdb_data_dictionary`,
          {
            dict_code: dictCode,
          }
        );
        this[targetArray] = response.data.msg;
      } catch (error) {
        console.error("数据加载失败:", error);
        this.$message.error("数据加载失败");
      }
    },

    // 根据生命周期显示名称获取对应的字典代码
    getOperationStatusCode(displayName) {
      if (!displayName || !this.operationstatus || this.operationstatus.length === 0) {
        return "D00035"; // 默认值
      }

      // 在生命周期数据字典中查找匹配的项
      const found = this.operationstatus.find(item => item.dict_name === displayName);
      return found ? found.dict_code : "D00035"; // 如果找不到，返回默认值
    },

    // 加载用户列表
    async loadUsersList() {
      try {
        const response = await this.$axios.post('/api/get_all_users_real_name');
        this.usersList = response.data.msg;
      } catch (error) {
        console.error('用户列表加载失败:', error);
        this.$message.error('用户列表加载失败');
      }
    },

    // 检查VMware关联
    async checkVmwareAssociation(managementIp) {
      if (!managementIp) {
        this.vmwareAssociation.hasAssociation = false;
        this.vmwareAssociation.esxiIp = "";
        return;
      }

      try {
        const response = await this.$axios.post('/api/get_vmware_hosts', {
          vm_ip: managementIp,
          currentPage: 1,
          pageSize: 1
        });

        if (response.data.code === 0 && response.data.msg && response.data.msg.length > 0) {
          const vmwareHost = response.data.msg[0];
          this.vmwareAssociation.hasAssociation = true;
          this.vmwareAssociation.esxiIp = vmwareHost.esxi_ip || "";
        } else {
          this.vmwareAssociation.hasAssociation = false;
          this.vmwareAssociation.esxiIp = "";
        }
      } catch (error) {
        console.error('检查VMware关联失败:', error);
        this.vmwareAssociation.hasAssociation = false;
        this.vmwareAssociation.esxiIp = "";
      }
    },



    // 新增效果实现
    handleAdd() {
      this.dialogVisible.add = !this.dialogVisible.add;
      this.formData = {
        id: null,
        management_ip: "",
        hostname: "",
        function_purpose: "",
        admin1: "",
        admin2: "",
        host_ip: "",
        host_ip_display: "",
        operating_system: "",
        data_center1: "",
        operation_status: "D00035", // 生命周期默认值：正常
        monitoring_requirement: "是", // 新增：需要监控默认值
        monitoring_requirement_description: "", // 新增：不监控原因
        is_monitored: "是", // 默认值
      };
      // 重置VMware关联状态
      this.vmwareAssociation.hasAssociation = false;
      this.vmwareAssociation.esxiIp = "";
      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.addFormRef) {
        this.$nextTick(() => {
          this.$refs.addFormRef.resetFields();
        });
      }
    },

    // 编辑按钮实现
    async handleEdit(_, row) {
      this.dialogVisible.edit = true;
      this.formData.id = row.id;
      this.formData.management_ip = row.management_ip;
      this.formData.hostname = row.hostname;
      this.formData.function_purpose = row.function_purpose;
      this.formData.admin1 = row.admin1;
      this.formData.admin2 = row.admin2;
      this.formData.host_ip = row.host_ip;
      this.formData.host_ip_display = row.host_ip_display || row.host_ip;
      // 操作系统：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.operating_system = row.operating_system_code || row.operating_system;
      // 所属机房：使用原始字典代码字段，如果不存在则使用显示名称
      this.formData.data_center1 = row.data_center1_code || row.data_center1;
      // 生命周期状态：使用原始字典代码字段，如果不存在则转换显示名称
      this.formData.operation_status = row.operation_status_code || this.getOperationStatusCode(row.operation_status);
      //this.formData.admin = row.admin;
      // this.formData.app_system_id = row.app_system_id; // 虚拟机所属系统 - 不再需要设置
      // this.formData.virtual_host_ip = row.virtual_host_ip;
      // this.formData.data_center2 = row.data_center2;
      // this.formData.is_monitored = row.is_monitored;
      this.formData.monitoring_requirement = row.monitoring_requirement; // 新增：需要监控
      this.formData.monitoring_requirement_description = row.monitoring_requirement_description; // 新增：不监控原因

      // 检查VMware关联
      await this.checkVmwareAssociation(row.management_ip);

      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.editFormRef) {
        this.$nextTick(() => {
          this.$refs.editFormRef.clearValidate();
        });
      }
    },

    // 删除效果实现
    handleDelete(_, row) {
      this.dialogVisible.delete = !this.dialogVisible.delete;
      this.formData.id = row.id;
      this.formData.management_ip = row.management_ip;
    },
    //导出数据
    exportData() {
      const table = this.$refs.table; // 获取 el-table 实例
      const columns = table.columns; // 获取表头
      const headers = columns.map((col) => col.label); // 获取表头
      const data = this.userArr.map((row) =>
        columns.map((col) => row[col.property])
      ); // 获取表格数据

      const wsData = [headers, ...data]; // 将表头和数据合并
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const blob = new Blob([wbout], { type: "application/octet-stream" });
      saveAs(blob, "虚拟机信息.xlsx");
    },

    // 获取生命周期标签类型（5个状态：正常、故障、闲置、报废、预报废）
    getLifecycleTagType(status) {
      switch (status) {
        case '正常':
          return 'success';    // 绿色 - 设备正常运行
        case '故障':
          return 'danger';     // 红色 - 设备出现故障
        case '闲置':
          return 'info';       // 蓝色 - 设备暂时不使用但可用
        case '报废':
          return 'info';       // 灰色 - 设备已报废，不再使用
        case '预报废':
          return 'warning';    // 橙色 - 设备即将报废
        default:
          // 兼容其他可能的状态表述
          if (status && status.includes('正常')) {
            return 'success';
          }
          if (status && status.includes('故障')) {
            return 'danger';
          }
          if (status && status.includes('闲置')) {
            return 'info';
          }
          if (status && (status.includes('报废') && !status.includes('预'))) {
            return 'info';
          }
          if (status && status.includes('预报废')) {
            return 'warning';
          }
          // 默认为信息色
          return 'info';
      }
    },

    // 处理从发现结果页面传递的参数
    handleAddFromDiscovery() {
      // 打开添加对话框
      this.dialogVisible.add = true;

      // 从 URL 参数中获取数据
      const { ip_address, hostname, open_ports } = this.$route.query;

      // 只填充管理IP
      this.formData = {
        management_ip: ip_address || '',
        hostname: hostname || '',
        function_purpose: '',
        admin1: '',
        admin2: '',
        host_ip: '',
        operating_system: '',
        data_center1: '',
        is_monitored: '',
        remarks: open_ports ? `开放端口: ${open_ports}` : ''
      };

      // 在下一个事件循环中重置表单验证状态
      if (this.$refs.addFormRef) {
        this.$nextTick(() => {
          this.$refs.addFormRef.resetFields();
          // 显示提示消息
          this.$message.info('请完善资产信息并提交');
        });
      }

      // 清除URL参数，避免刷新页面时重复打开对话框
      this.$router.replace({ path: this.$route.path });
    },

    // 需要监控变化处理方法
    handleMonitoringRequirementChange(value) {
      // 当需要监控为"是"时，清空不监控原因
      if (value === '是') {
        this.formData.monitoring_requirement_description = '';
      }
    },

    // 自定义验证器：不监控原因
    validateMonitoringDescription(rule, value, callback) {
      // 当需要监控为"否"时，不监控原因为必填项
      if (this.formData.monitoring_requirement === '否' && (!value || value.trim() === '')) {
        callback(new Error('当需要监控为"否"时，不监控原因为必填项'));
      } else {
        callback();
      }
    },

    // 检查Zabbix配置状态
    async checkZabbixConfigStatus() {
      try {
        const result = await getZabbixConfigStatus();
        if (result.success) {
          this.zabbixHostInfo.enabled = result.data.config.enabled && result.data.connection.success;
        } else {
          console.warn('获取Zabbix配置状态失败:', result.error);
          this.zabbixHostInfo.enabled = false;
        }
      } catch (error) {
        console.error('检查Zabbix配置状态失败:', error);
        this.zabbixHostInfo.enabled = false;
      }
    },

    // 手动查询ESXi主机IP
    async queryHostIpManually() {
      if (!this.formData.management_ip) {
        this.$message.warning('请先输入管理IP');
        return;
      }

      if (!this.zabbixHostInfo.enabled) {
        this.$message.warning('外部接口服务未启用或连接失败');
        return;
      }

      try {
        this.zabbixHostInfo.loading = true;
        this.zabbixHostInfo.error = null;

        const result = await getZabbixHostInfoWithCache(this.formData.management_ip, true);

        if (result.success && result.data) {
          // 更新ESXi主机IP
          this.formData.host_ip = result.data.host_ip || '';
          this.zabbixHostInfo.lastUpdated = new Date().toLocaleString();

          if (result.data.host_ip) {
            this.$message.success(`成功获取ESXi主机IP: ${result.data.host_ip}`);
          } else {
            this.$message.warning('未找到ESXi主机IP信息');
          }
        } else {
          this.zabbixHostInfo.error = result.error || '查询失败';
          this.$message.error('查询ESXi主机IP失败: ' + this.zabbixHostInfo.error);
          console.error('Zabbix API响应错误详情:', result);
        }
      } catch (error) {
        console.error('手动查询ESXi主机IP失败:', error);
        this.zabbixHostInfo.error = error.message || '查询失败';
        this.$message.error('查询ESXi主机IP失败: ' + this.zabbixHostInfo.error);
      } finally {
        this.zabbixHostInfo.loading = false;
      }
    },

    // 检查虚拟机管理IP是否重复
    async checkVmManagementIpDuplicate(ip, id = null) {
      if (!ip) return { exists: false };

      try {
        const response = await this.$axios.post('/api/check_vm_management_ip', {
          management_ip: ip,
          id: id,
          operation_status: this.formData.operation_status // 传递当前的生命周期状态
        });
        return response.data;
      } catch (error) {
        console.error('检查虚拟机管理IP失败:', error);
        throw new Error('检查虚拟机管理IP失败，请稍后重试');
      }
    },

    // 自动查询ESXi主机IP（防抖）
    autoQueryHostIp(managementIp) {
      if (!managementIp || !this.zabbixHostInfo.enabled) {
        return;
      }

      // 设置加载状态
      this.zabbixHostInfo.loading = true;
      this.zabbixHostInfo.error = null;

      this.debouncedHostInfoQuery(managementIp, (result) => {
        this.zabbixHostInfo.loading = false;

        if (result.success && result.data) {
          if (result.data.host_ip) {
            // 成功获取到ESXi主机IP
            this.formData.host_ip = result.data.host_ip;
            this.zabbixHostInfo.lastUpdated = new Date().toLocaleString();
            this.zabbixHostInfo.error = null;

            // 显示成功提示（可选，避免过于频繁的提示）
            console.log(`自动获取ESXi主机IP成功: ${result.data.host_ip}`);
          } else {
            // API调用成功但未找到ESXi主机IP
            this.zabbixHostInfo.error = '未找到对应的ESXi主机IP信息';
            console.log(`未找到管理IP ${managementIp} 对应的ESXi主机IP`);
          }
        } else {
          // API调用失败
          this.zabbixHostInfo.error = result.error || '自动查询失败';
          console.error(`自动查询ESXi主机IP失败: ${this.zabbixHostInfo.error}`);
        }
      });
    },
  },
};
</script>

<style scoped>
/* 统一操作按钮区样式 */
.unified-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: nowrap;
}

.action-bar-left,
.action-bar-right {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
}

/* 按钮容器 */
.button-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 10px;
}

/* 搜索按钮对齐 */
.search-buttons-col {
  display: flex;
  align-items: center;
}

.search-buttons {
  margin-bottom: 0;
  text-align: right;
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .unified-action-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-bar-left,
  .action-bar-right {
    margin-bottom: 8px;
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .action-bar-right {
    justify-content: flex-end;
  }
}
</style>