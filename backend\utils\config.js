/**
 * 系统配置工具
 * 统一管理系统配置项，避免重复配置
 */

/**
 * 获取系统基础URL
 * 动态构建，统一使用PORT配置
 * @returns {string} 系统基础URL
 */
const getBaseUrl = () => {
  const serverHost = process.env.SERVER_HOST || 'localhost';
  const serverPort = process.env.PORT || '3000';
  return `http://${serverHost}:${serverPort}`;
};

/**
 * 获取服务器配置
 * @returns {object} 服务器配置对象
 */
const getServerConfig = () => {
  return {
    host: process.env.SERVER_HOST || 'localhost',
    port: parseInt(process.env.PORT || '3000'),
    baseUrl: getBaseUrl()
  };
};



/**
 * 获取定时任务配置
 * @returns {object} 定时任务配置对象
 */
const getScheduleConfig = () => {
  return {
    vmHostIpUpdateSchedule: process.env.VM_HOST_IP_UPDATE_SCHEDULE || '0 2 * * *',
    vmHostIpUpdateEnabled: process.env.VM_HOST_IP_UPDATE_ENABLED === 'true'
  };
};

/**
 * 获取日志配置
 * @returns {object} 日志配置对象
 */
const getLogConfig = () => {
  return {
    hostIpUpdateLogLevel: process.env.HOST_IP_UPDATE_LOG_LEVEL || 'info',
    hostIpUpdateLogRetentionDays: parseInt(process.env.HOST_IP_UPDATE_LOG_RETENTION_DAYS) || 30
  };
};

/**
 * 验证所有配置
 * @returns {object} 全局配置验证结果
 */
const validateAllConfigs = () => {
  const results = {
    server: { isValid: true, errors: [] },
    overall: { isValid: true, errors: [] }
  };

  // 检查服务器配置
  const serverConfig = getServerConfig();
  if (!serverConfig.host || !serverConfig.port) {
    results.server.isValid = false;
    results.server.errors.push('Server host and port must be configured');
  }

  // 计算总体验证结果
  results.overall.isValid = results.server.isValid;
  results.overall.errors = [...results.server.errors];

  return results;
};

module.exports = {
  getBaseUrl,
  getServerConfig,
  getScheduleConfig,
  getLogConfig,
  validateAllConfigs
};