-- 更新 v_cmdb_vm_registry 视图以包含VMware关联的ESXi主机IP
-- 版本: *******
-- 日期: 2025-01-14
-- 描述: 更新虚拟机视图，通过关联 cmdb_vmware_info_auto_update 表来显示ESXi主机IP

-- 删除现有视图
DROP VIEW IF EXISTS public.v_cmdb_vm_registry CASCADE;

-- 重新创建视图，包含VMware关联
CREATE VIEW public.v_cmdb_vm_registry AS
SELECT 
    t.id,
    t.management_ip,
    t.hostname,
    t.function_purpose,
    t.admin1,
    t.admin2,
    t.host_ip,
    -- 通过VMware关联获取ESXi主机IP，如果关联不到则显示原有的host_ip
    COALESCE(v.esxi_ip::text, t.host_ip) AS host_ip_display,
    COALESCE(t2.dict_name, t.operating_system) AS operating_system,
    COALESCE(t3.dict_name, t.data_center1) AS data_center1,
    COALESCE(t5.dict_name, t.operation_status) AS operation_status,
    t.admin,
    t.app_system_id,
    t.virtual_host_ip,
    t.data_center2,
    t.is_monitored,
    CASE
        WHEN (t.monitoring_requirement = true) THEN '是'::text
        WHEN (t.monitoring_requirement = false) THEN '否'::text
        ELSE '否'::text
    END AS monitoring_requirement,
    t.monitoring_requirement_description,
    CASE
        WHEN (EXISTS ( SELECT 1
           FROM public.v_cmdb_discovery_results dr
          WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
        ELSE '离线'::text
    END AS online_status,
    t.weak_password_exists,
    t.weak_password_correction_date,
    t.created_at,
    t.created_by,
    t.updated_at,
    t.updated_by,
    -- 添加原始字典代码字段，用于编辑时回显
    t.operation_status as operation_status_code,
    t.data_center1 as data_center1_code,
    t.operating_system as operating_system_code
FROM public.cmdb_vm_registry t
LEFT JOIN public.cmdb_data_dictionary t2 ON ((t2.del_flag)::text = '0'::text) AND ((t2.dict_code)::text = (t.operating_system)::text)
LEFT JOIN public.cmdb_data_dictionary t3 ON ((t3.del_flag)::text = '0'::text) AND ((t3.dict_code)::text = (t.data_center1)::text)
LEFT JOIN public.cmdb_data_dictionary t5 ON ((t5.del_flag)::text = '0'::text) AND ((t5.dict_code)::text = (t.operation_status)::text)
-- 新增：关联VMware虚拟化信息表
LEFT JOIN public.cmdb_vmware_info_auto_update v ON (t.management_ip)::text = (v.vm_ip)::text AND v.is_deleted = false
WHERE ((t.del_flag)::text = '0'::text);

-- 设置视图所有者
ALTER VIEW public.v_cmdb_vm_registry OWNER TO postgres;

-- 添加视图注释
COMMENT ON VIEW public.v_cmdb_vm_registry IS '虚拟机视图：包含监控需求字段、生命周期字段和VMware关联的ESXi主机IP，布尔值转换为中文显示';

-- 授予权限
GRANT SELECT ON TABLE public.v_cmdb_vm_registry TO cjmonitor;

-- 输出成功信息
SELECT '✓ v_cmdb_vm_registry 视图已成功更新，现在包含VMware关联的ESXi主机IP' as result;

-- 验证视图是否正确创建
DO $$
DECLARE
    view_count INTEGER;
    column_count INTEGER;
BEGIN
    -- 检查视图是否存在
    SELECT COUNT(*) INTO view_count
    FROM information_schema.views 
    WHERE table_schema = 'public' AND table_name = 'v_cmdb_vm_registry';
    
    IF view_count = 1 THEN
        RAISE NOTICE '✓ 视图验证通过：v_cmdb_vm_registry 视图已成功重建';
    ELSE
        RAISE EXCEPTION '✗ 视图验证失败：v_cmdb_vm_registry 视图重建失败';
    END IF;
    
    -- 检查 host_ip_display 字段是否存在
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
      AND table_name = 'v_cmdb_vm_registry' 
      AND column_name = 'host_ip_display';
    
    IF column_count = 1 THEN
        RAISE NOTICE '✓ 字段验证通过：host_ip_display 字段已成功添加';
    ELSE
        RAISE EXCEPTION '✗ 字段验证失败：host_ip_display 字段添加失败';
    END IF;
END $$;
