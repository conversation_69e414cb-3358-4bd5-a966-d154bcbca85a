# 基础URL 前端访问地址
BASE_URL='https://dop.cjfco.com.cn/'

# 服务器配置，后端访问地址
SERVER_HOST=*************
# 后端端口
PORT=3000

DB_HOST='*************'
DB_USER='postgres'
DB_PASSWORD='cjqh@1234'
DB_NAME='cmdb'
DB_PORT=5432

LDAP_URL=ldap://**************:389
LDAP_BASE_DN=ou=users,ou=cjsc,dc=cjsc,dc=com,dc=cn
LDAP_USER_DN_TEMPLATE=cn={{username}},ou=users,ou=cjsc,dc=cjsc,dc=com,dc=cn
LDAP_ENABLED=true

JWT_SECRET='wodemiyaonibuzhidaohaha'
JWT_EXPIRES_IN='8h'

# 文件存储配置 - 统一使用直接文件流上传下载
FILE_UPLOAD_BASE_PATH=/home/<USER>/uploads
FILE_UPLOAD_OA_PATH=/home/<USER>/uploads/oa_process
FILE_UPLOAD_SIGNED_PATH=/home/<USER>/uploads/signed_archive
FILE_UPLOAD_OPERATION_PATH=/home/<USER>/uploads/operation_sheet
FILE_UPLOAD_SUPPLEMENTARY_PATH=/home/<USER>/uploads/supplementary_material
FILE_UPLOAD_TEMPLATES_PATH=/home/<USER>/uploads/change_templates
FILE_UPLOAD_EVENT_TEMPLATES_PATH=/home/<USER>/uploads/event_templates
FILE_UPLOAD_EVENT_FILES_PATH=/home/<USER>/uploads/event_files

# FILE_URL_PREFIX 配置（可选）- 仅用于向后兼容，实际下载使用直接文件流
# 在生产环境中可以设置为实际域名，开发环境保持localhost即可
FILE_URL_PREFIX=https://dop.cjfco.com.cn/files

# CORS 安全配置
# 允许的域名白名单，使用逗号分隔，生产环境必须严格配置
CORS_ALLOWED_ORIGINS=http://*************:9000,http://*************:3000,http://*************:9000,http://*************:3000,http://dop.cjfco.com.cn,https://dop.cjfco.com.cn

# CORS 配置选项
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With,Accept,Origin

# 环境模式 (development/production)
NODE_ENV=production

# CORS 严格模式 (true/false) - 生产环境建议设置为true
CORS_STRICT_MODE=true

# CORS 日志记录 (true/false) - 记录被拒绝的请求
CORS_LOG_REJECTED=true

# HTTP 安全响应头配置
# 是否启用安全响应头 (true/false)
SECURITY_HEADERS_ENABLED=true

# X-Download-Options 配置
SECURITY_DOWNLOAD_OPTIONS=noopen

# X-Permitted-Cross-Domain-Policies 配置
SECURITY_CROSS_DOMAIN_POLICIES=none

# Content-Security-Policy 配置
SECURITY_CSP_ENABLED=true
# CSP 报告模式 (true/false) - 设置为true时使用Content-Security-Policy-Report-Only
SECURITY_CSP_REPORT_ONLY=true

# Referrer-Policy 配置
SECURITY_REFERRER_POLICY=strict-origin-when-cross-origin

# Strict-Transport-Security 配置 (仅HTTPS环境生效)
SECURITY_HSTS_ENABLED=true
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_HSTS_INCLUDE_SUBDOMAINS=true
SECURITY_HSTS_PRELOAD=true

# Permissions-Policy 配置
SECURITY_PERMISSIONS_POLICY=geolocation=(), microphone=(), camera=(), payment=(), usb=()

# Office文档预览配置
# client_side: 纯前端预览（推荐，无需第三方服务，支持Excel完整预览）
# fallback: 仅下载，不预览
OFFICE_PREVIEW_METHOD=client_side

